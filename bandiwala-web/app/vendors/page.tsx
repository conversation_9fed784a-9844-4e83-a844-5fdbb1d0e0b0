'use client';

import React, { useEffect } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { useRouter } from 'next/navigation';
import Navbar from '@/components/Header';
import Footer from '@/components/Footer';
import { vendorService } from '@/services/api';
import { useQuery } from '@tanstack/react-query';
import { getImageUrl } from '@/utils/imageUtils';
import { useAuth } from '@/contexts/AuthContext';
import { ShoppingBag, Star, ArrowRight } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';


interface Vendor {
  _id: string;
  name: string;
  description: string;
  slug: string;
  rating: number;
  location: string;
  image: string;
  deliveryTime: string;
  deliveryFee: number;
  minOrderValue: number;
  isActive: boolean;
}

export default function VendorsPage() {
  const { isAuthenticated, isLoading: authLoading } = useAuth();
  const router = useRouter();

  // Redirect to login if not authenticated
  useEffect(() => {
    if (!authLoading && !isAuthenticated) {
      router.push('/login');
    }
  }, [isAuthenticated, authLoading, router]);

  const { data, isLoading, error } = useQuery({
    queryKey: ['vendors'],
    queryFn: async () => {
      const response = await vendorService.getAllVendors();
      return response.data;
    },
    enabled: isAuthenticated // Only run query when authenticated
  });

  // Show loading while checking authentication
  if (authLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-16 w-16 border-4 border-bandiwala-orange border-t-transparent"></div>
      </div>
    );
  }

  // Show login prompt if not authenticated
  if (!isAuthenticated) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="bg-white p-8 rounded-xl shadow-lg max-w-md w-full mx-4 text-center">
          <ShoppingBag className="h-16 w-16 text-bandiwala-orange mx-auto mb-4" />
          <h2 className="text-2xl font-bold text-gray-900 mb-2">Login Required</h2>
          <p className="text-gray-600 mb-6">
            Please log in to view our vendors and start ordering.
          </p>
          <Link href="/login">
            <button className="w-full bg-gradient-to-r from-bandiwala-orange to-bandiwala-red hover:from-bandiwala-red hover:to-bandiwala-orange text-white font-semibold px-6 py-3 rounded-full transition-all duration-300">
              Login to Continue
            </button>
          </Link>
        </div>
      </div>
    );
  }

  console.log(data)
  return (
    <div className="min-h-screen flex flex-col">
      <Navbar showBackButton={true} />
      <main className="flex-1">
        {/* <Profile /> */}
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="py-8">
            <div className="text-center mb-12">
              <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-4">
                Popular <span className="text-bandiwala-orange">Vendors</span>
              </h1>
              <p className="text-lg text-gray-600 max-w-2xl mx-auto">
                Discover amazing local vendors and enjoy delicious food delivered right to your doorstep
              </p>
            </div>

            {isLoading && (
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8">
                {[...Array(8)].map((_, i) => (
                  <Card key={i} className="overflow-hidden border-0 shadow-lg bg-white rounded-2xl h-full flex flex-col">
                    <div className="h-80 bg-gray-200 animate-pulse"></div>
                    <CardContent className="p-4 flex-1 flex flex-col">
                      <div className="flex justify-between items-start mb-2">
                        <div className="h-5 bg-gray-200 rounded animate-pulse flex-1 mr-2"></div>
                        <div className="h-6 w-12 bg-gray-200 rounded-full animate-pulse"></div>
                      </div>
                      <div className="flex justify-between items-center">
                        <div className="h-4 bg-gray-200 rounded animate-pulse w-20"></div>
                        <div className="h-4 w-4 bg-gray-200 rounded animate-pulse"></div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            )}

            {error && (
              <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative" role="alert">
                <strong className="font-bold">Error!</strong>
                <span className="block sm:inline"> Failed to load vendors. Please try again later.</span>
              </div>
            )}

            {data && data.length > 0 && (
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8">
                {data.map((vendor: Vendor, index: number) => {
                  // Log the vendor data for debugging
                  console.log(`Rendering vendor card: ${vendor.name}, slug: ${vendor.slug}`);

                  return (
                    <Link href={`/vendors/${vendor.slug}`} key={vendor._id} prefetch={true} className="group">
                      <Card
                        className="overflow-hidden border-0 shadow-lg hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-2 bg-white rounded-2xl h-full flex flex-col animate-in fade-in slide-in-from-bottom-4"
                        style={{ animationDelay: `${index * 100}ms` }}
                      >
                        {/* 80% Image Section */}
                        <div className="relative h-80 overflow-hidden">
                          {vendor.image && (
                            <Image
                              src={getImageUrl(vendor.image)}
                              alt={vendor.name}
                              width={400}
                              height={320}
                              className="w-full h-full object-cover transition-transform duration-500 group-hover:scale-110"
                            />
                          )}
                          <div className="absolute inset-0 bg-gradient-to-t from-black/30 to-transparent"></div>
                          <div className="absolute top-4 left-4 bg-green-500 px-3 py-1.5 rounded-full text-xs font-bold text-white shadow-lg flex items-center">
                            <div className="w-2 h-2 bg-white rounded-full mr-2 animate-pulse"></div>
                            Open Now
                          </div>
                          {vendor.rating >= 4.0 && (
                            <div className="absolute top-4 right-4 bg-gradient-to-r from-bandiwala-yellow to-yellow-400 px-3 py-1.5 rounded-full text-xs font-bold text-gray-800 shadow-lg">
                              ⭐ Popular
                            </div>
                          )}
                        </div>

                        {/* 20% Info Section */}
                        <CardContent className="p-4 flex-1 flex flex-col">
                          <div className="flex justify-between items-start mb-2">
                            <h3 className="font-bold text-lg text-gray-900 group-hover:text-bandiwala-orange transition-colors duration-300 line-clamp-1 flex-1">
                              {vendor.name}
                            </h3>
                            <div className="flex items-center bg-yellow-50 px-2 py-1 rounded-full ml-2">
                              <Star className="h-3 w-3 fill-yellow-400 text-yellow-400 mr-1" />
                              <span className="text-xs font-bold text-gray-900">{vendor.rating}</span>
                            </div>
                          </div>

                          <div className="flex justify-between items-center">
                            <span className="text-sm font-bold text-bandiwala-red group-hover:text-bandiwala-orange transition-colors duration-300">
                              View Menu
                            </span>
                            <ArrowRight className="h-4 w-4 text-bandiwala-red group-hover:text-bandiwala-orange transform group-hover:translate-x-1 transition-all duration-300" />
                          </div>
                        </CardContent>
                      </Card>
                    </Link>
                  );
                })}
              </div>
            )}

            {data && data.length === 0 && (
              <div className="text-center py-16">
                <div className="max-w-md mx-auto">
                  <ShoppingBag className="h-24 w-24 text-gray-300 mx-auto mb-6" />
                  <h3 className="text-2xl font-bold text-gray-900 mb-4">No Vendors Available</h3>
                  <p className="text-gray-600 mb-8">
                    We&apos;re working hard to bring amazing vendors to your area. Check back soon!
                  </p>
                  <Link href="/">
                    <button className="bg-gradient-to-r from-bandiwala-orange to-bandiwala-red hover:from-bandiwala-red hover:to-bandiwala-orange text-white font-semibold px-8 py-3 rounded-full transition-all duration-300 transform hover:scale-105">
                      Explore Other Options
                    </button>
                  </Link>
                </div>
              </div>
            )}
          </div>
        </div>
      </main>
      <Footer />
    </div>
  );
}